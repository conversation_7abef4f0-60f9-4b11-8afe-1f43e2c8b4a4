# MotionStreamer 动作数据管理模块

## 概述

动作数据管理模块 (`motion_data_manager.py`) 是一个独立的代码文件，负责动作数据的保存、加载和管理功能。当Blender客户端接收到动作数据时，会自动保存动作数据，便于下次直接复用，不用开服务端也能调试。

## 主要功能

### 1. 自动保存动作数据
- 当从服务器接收到动作数据时，自动保存到本地文件
- 支持流式生成和非流式生成两种模式
- 保存格式包括JSON和numpy（大数据量时）

### 2. 手动保存当前动作
- 可以手动保存当前缓存的动作数据
- 支持自定义描述文本和元数据

### 3. 加载保存的动作数据
- 从文件加载之前保存的动作数据
- 自动应用到当前选择的骨架
- 支持JSON和numpy格式的自动识别

### 4. 数据管理
- 列出所有保存的动作数据
- 显示存储信息（文件数量、总大小等）
- 打开数据存储文件夹
- 删除不需要的动作数据

## 文件结构

```
blender_addon/
├── motion_data_manager.py    # 动作数据管理模块（新增）
├── __init__0.py             # 主插件文件（已修改）
└── mmd_driver.py            # MMD驱动模块
```

## 数据存储

### 默认存储位置
- **Windows**: `C:\Users\<USER>\Documents\MotionStreamer\saved_motions\`
- **Linux/Mac**: `~/MotionStreamer/saved_motions/`

### 文件格式
每个动作数据保存为JSON文件，包含以下信息：
```json
{
  "text": "动作描述文本",
  "mode": "pos",
  "timestamp": "20250930_144142",
  "frames": 60,
  "joints": 22,
  "motion_data": [...],
  "metadata": {
    "total_time": 2.5,
    "first_frame_time": 0.8,
    "generation_method": "streaming"
  }
}
```

### 大数据量优化
- 超过100帧的动作数据会同时保存numpy格式（.npy文件）
- 加载时优先使用numpy格式以提高速度
- JSON文件包含numpy文件的引用信息

## 使用方法

### 在Blender中使用

1. **自动保存**：
   - 生成动作后会自动保存到本地
   - 无需手动操作

2. **手动保存**：
   - 在"数据管理"面板中点击"保存当前动作"
   - 会保存当前缓存的动作数据

3. **加载动作**：
   - 点击"加载动作数据"按钮
   - 选择要加载的JSON文件
   - 动作会自动应用到当前骨架

4. **管理数据**：
   - 查看当前动作信息
   - 查看存储统计信息
   - 打开数据文件夹进行文件管理

### 编程接口使用

```python
from blender_addon.motion_data_manager import get_motion_data_manager

# 获取管理器实例
manager = get_motion_data_manager()

# 保存动作数据
saved_file = manager.save_motion_data(
    motion_data=motion_data,
    text="行走动作",
    mode="pos",
    metadata={"custom_info": "value"}
)

# 加载动作数据
data = manager.load_motion_data(file_path)

# 列出所有保存的动作
motions = manager.list_saved_motions()

# 获取存储信息
info = manager.get_storage_info()
```

## 新增的UI面板

### 数据管理面板
位置：View3D > Sidebar > MotionStreamer > 数据管理

功能：
- **当前动作**：显示当前缓存的动作信息
- **保存当前动作**：手动保存当前动作
- **加载动作数据**：从文件加载动作
- **打开数据文件夹**：打开存储文件夹
- **存储信息**：显示文件数量和总大小

## 新增的操作符

1. **MOTIONSTREAMER_OT_load_motion_data**
   - 功能：加载保存的动作数据
   - 快捷键：无
   - 描述：从文件选择器加载动作数据

2. **MOTIONSTREAMER_OT_save_current_motion**
   - 功能：手动保存当前动作
   - 快捷键：无
   - 描述：保存当前缓存的动作数据

3. **MOTIONSTREAMER_OT_open_data_folder**
   - 功能：打开数据存储文件夹
   - 快捷键：无
   - 描述：在系统文件管理器中打开数据文件夹

## 优势

1. **离线调试**：保存的动作数据可以在没有服务器的情况下使用
2. **数据复用**：可以重复使用之前生成的动作数据
3. **性能优化**：大数据量使用numpy格式提高加载速度
4. **用户友好**：直观的UI界面，易于操作
5. **数据安全**：本地存储，数据不会丢失
6. **跨平台**：支持Windows、Linux、Mac系统

## 测试

运行测试脚本验证功能：
```bash
python test_motion_data_manager.py
```

测试内容包括：
- 创建测试动作数据
- 保存动作数据
- 列出保存的动作
- 加载动作数据
- 获取存储信息

## 注意事项

1. **文件命名**：文件名基于动作描述文本生成，特殊字符会被过滤
2. **存储空间**：长时间使用可能积累大量文件，建议定期清理
3. **兼容性**：保存的数据格式与当前版本兼容，升级时可能需要转换
4. **权限**：确保对存储目录有读写权限

## 未来扩展

1. **数据压缩**：对大文件进行压缩存储
2. **云同步**：支持云端存储和同步
3. **版本管理**：支持动作数据的版本控制
4. **批量操作**：支持批量导入导出
5. **预览功能**：在加载前预览动作数据

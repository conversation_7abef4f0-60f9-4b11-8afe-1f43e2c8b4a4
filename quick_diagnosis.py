"""
快速诊断脚本
用于检查MotionStreamer MMD驱动系统的状态
"""

import os
import sys
import json

def check_files():
    """检查必要文件是否存在"""
    print("🔍 检查文件完整性...")
    
    required_files = [
        'blender_addon/__init__0.py',
        'blender_addon/mmd_driver.py',
        'blender_addon/test_mmd_driver.py',
        'websocket_server.py',
        'blender_debug_test.py'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({size} bytes)")
        else:
            print(f"   ❌ {file_path} - 文件不存在")
            missing_files.append(file_path)
    
    return len(missing_files) == 0

def check_test_data():
    """检查测试数据"""
    print("\n📊 检查测试数据...")
    
    test_files = [
        'test_motion_data.json',
        'demo_walking_motion.json'
    ]
    
    for file_path in test_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r') as f:
                    data = json.load(f)
                
                frames = len(data)
                joints = len(data[0]) if data else 0
                
                print(f"   ✅ {file_path}:")
                print(f"      - 帧数: {frames}")
                print(f"      - 关节数: {joints}")
                
                if joints == 22 and isinstance(data[0][0], list) and len(data[0][0]) == 3:
                    print(f"      - 格式: 正确 (22关节3D位置)")
                elif joints == 66:
                    print(f"      - 格式: 66维展平数据")
                else:
                    print(f"      - 格式: 异常 (需要检查)")
                    
            except Exception as e:
                print(f"   ❌ {file_path} - 读取失败: {e}")
        else:
            print(f"   ⚠️  {file_path} - 文件不存在")

def check_dependencies():
    """检查Python依赖"""
    print("\n📦 检查Python依赖...")
    
    required_modules = [
        'numpy',
        'torch',
        'websockets',
        'asyncio',
        'json'
    ]
    
    missing_modules = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"   ✅ {module}")
        except ImportError:
            print(f"   ❌ {module} - 未安装")
            missing_modules.append(module)
    
    return len(missing_modules) == 0

def analyze_mmd_driver():
    """分析MMD驱动器代码"""
    print("\n🔧 分析MMD驱动器...")
    
    driver_file = 'blender_addon/mmd_driver.py'
    if not os.path.exists(driver_file):
        print("   ❌ MMD驱动器文件不存在")
        return False
    
    try:
        with open(driver_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键类和方法
        checks = [
            ('class MMDDriver', 'MMDDriver类'),
            ('def apply_motion_data', '动作应用方法'),
            ('def _apply_position_data', '位置数据处理'),
            ('def find_bone_name', '骨骼查找方法'),
            ('joint_to_mmd_mapping', '关节映射表'),
            ('alternative_bone_names', '备用骨骼名称')
        ]
        
        for check, description in checks:
            if check in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - 缺失")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return False

def check_blender_plugin():
    """检查Blender插件"""
    print("\n🎨 检查Blender插件...")
    
    plugin_file = 'blender_addon/__init__0.py'
    if not os.path.exists(plugin_file):
        print("   ❌ 主插件文件不存在")
        return False
    
    try:
        with open(plugin_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键组件
        checks = [
            ('bl_info', '插件信息'),
            ('import mmd_driver', 'MMD驱动器导入'),
            ('apply_motion_to_armature', '动作应用函数'),
            ('MOTIONSTREAMER_OT_generate_motion', '动作生成操作'),
            ('def register()', '插件注册函数')
        ]
        
        for check, description in checks:
            if check in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - 缺失")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 分析失败: {e}")
        return False

def generate_diagnosis_report():
    """生成诊断报告"""
    print("\n📋 生成诊断报告...")
    
    report = {
        'timestamp': __import__('datetime').datetime.now().isoformat(),
        'files_ok': check_files(),
        'dependencies_ok': check_dependencies(),
        'mmd_driver_ok': analyze_mmd_driver(),
        'plugin_ok': check_blender_plugin()
    }
    
    # 保存报告
    with open('diagnosis_report.json', 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"   ✅ 诊断报告已保存到: diagnosis_report.json")
    
    return report

def print_recommendations(report):
    """打印建议"""
    print("\n💡 建议和下一步:")
    
    if not report['files_ok']:
        print("   🔧 请确保所有必要文件都存在")
    
    if not report['dependencies_ok']:
        print("   📦 请安装缺失的Python模块")
        print("      pip install numpy torch websockets")
    
    if not report['mmd_driver_ok']:
        print("   🛠️  MMD驱动器需要修复")
    
    if not report['plugin_ok']:
        print("   🎨 Blender插件需要修复")
    
    if all(report.values()):
        print("   ✅ 系统文件完整，可以进行测试")
        print("   📝 请按照 DEBUG_INSTRUCTIONS.md 进行调试")
    else:
        print("   ⚠️  发现问题，请先修复上述问题")

def main():
    """主函数"""
    print("="*60)
    print("MotionStreamer MMD驱动系统快速诊断")
    print("="*60)
    
    # 检查当前目录
    current_dir = os.getcwd()
    print(f"📁 当前目录: {current_dir}")
    
    # 运行各项检查
    check_test_data()
    
    # 生成报告
    report = generate_diagnosis_report()
    
    # 打印建议
    print_recommendations(report)
    
    print("\n" + "="*60)
    print("诊断完成")
    print("="*60)

if __name__ == "__main__":
    main()

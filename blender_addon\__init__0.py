bl_info = {
    "name": "MotionStreamer MMD Driver",
    "author": "MotionStreamer Team",
    "version": (1, 0, 0),
    "blender": (3, 0, 0),
    "location": "View3D > Sidebar > MotionStreamer0",
    "description": "通过WebSocket连接MotionStreamer，实现文本到MMD动作的实时生成",
    "category": "Animation",
}
print("MotionStreamer: WebSocket库加载--------------------")
import bpy
import json
import threading
import time
from bpy.props import StringProperty, BoolProperty, EnumProperty, IntProperty, FloatProperty
from bpy.types import Panel, Operator, PropertyGroup

# 导入MMD驱动模块
try:
    from . import mmd_driver
    print("MotionStreamer: MMD驱动模块加载成功")
except ImportError as e:
    print(f"MotionStreamer: MMD驱动模块加载失败 - {e}")
    mmd_driver = None

# 可选导入外部库
WEBSOCKETS_AVAILABLE = False
try:
    import asyncio
    import websockets
    WEBSOCKETS_AVAILABLE = True
    print("MotionStreamer: WebSocket库加载成功")
except ImportError:
    print("MotionStreamer: 警告 - websockets库未安装，WebSocket功能将不可用")

# 全局变量
motion_data_cache = []
is_playing = False
current_frame = 0
websocket_connection = None
connection_status = "未连接"
event_loop = None
loop_thread = None

# 添加性能监控变量
frame_count = 0
last_frame_time = 0
frame_rate_samples = []
def get_or_create_event_loop():
    """获取或创建事件循环"""
    global event_loop, loop_thread
    
    if event_loop is None or event_loop.is_closed():
        def run_loop():
            global event_loop
            event_loop = asyncio.new_event_loop()
            asyncio.set_event_loop(event_loop)
            event_loop.run_forever()
        
        loop_thread = threading.Thread(target=run_loop, daemon=True)
        loop_thread.start()
        
        # 等待事件循环启动
        while event_loop is None:
            time.sleep(0.01)
    
    return event_loop

def run_async_task(coro):
    """在事件循环中运行异步任务"""
    loop = get_or_create_event_loop()
    future = asyncio.run_coroutine_threadsafe(coro, loop)
    return future

class MotionStreamerProperties(PropertyGroup):
    """MotionStreamer属性组"""
    
    # WebSocket连接设置
    server_host: StringProperty(
        name="服务器地址",
        description="MotionStreamer服务器地址",
        default="localhost"
    )
    
    server_port: IntProperty(
        name="端口",
        description="MotionStreamer服务器端口",
        default=8765,
        min=1,
        max=65535
    )
    
    # 文本输入
    motion_text: StringProperty(
        name="动作描述",
        description="输入要生成的动作描述文本",
        default="A person is walking forward"
    )
    
    # 动作模式
    motion_mode: EnumProperty(
        name="动作模式",
        description="选择动作数据模式",
        items=[
            ('pos', '位置模式', '使用关节位置数据'),
            ('rot', '旋转模式', '使用关节旋转数据')
        ],
        default='pos'
    )
    
    # MMD模型选择
    target_armature: StringProperty(
        name="目标骨架",
        description="选择要驱动的MMD模型骨架",
        default=""
    )
    
    # 播放设置
    auto_play: BoolProperty(
        name="自动播放",
        description="生成动作后自动播放",
        default=True
    )
    
    playback_speed: FloatProperty(
        name="播放速度",
        description="动作播放速度倍数",
        default=1.0,
        min=0.1,
        max=5.0
    )
    
    loop_animation: BoolProperty(
        name="循环播放",
        description="循环播放生成的动作",
        default=True
    )

class MOTIONSTREAMER_OT_connect(Operator):
    """连接到MotionStreamer服务器"""
    bl_idname = "motionstreamer.connect"
    bl_label = "连接服务器"
    bl_description = "连接到MotionStreamer WebSocket服务器"
    
    def execute(self, context):
        if not WEBSOCKETS_AVAILABLE:
            self.report({'ERROR'}, "WebSocket功能不可用，请安装websockets库")
            return {'CANCELLED'}
            
        props = context.scene.motionstreamer_props
        
        def connect_websocket():
            global websocket_connection, connection_status
            
            try:
                async def connect():
                    global websocket_connection, connection_status
                    uri = f"ws://{props.server_host}:{props.server_port}"
                    
                    try:
                        websocket_connection = await websockets.connect(
                            uri,
                            ping_interval=20,  # 每20秒发送ping
                            ping_timeout=10,   # ping超时时间10秒
                            close_timeout=10,  # 关闭超时时间10秒
                            max_size=10**7     # 增加最大消息大小到10MB
                        )
                        connection_status = "已连接"
                        print(f"已连接到MotionStreamer服务器: {uri}")
                        
                        # 发送ping测试连接
                        await websocket_connection.send(json.dumps({"command": "ping"}))
                        response = await websocket_connection.recv()
                        data = json.loads(response)
                        
                        if data.get('status') == 'pong':
                            print("服务器连接测试成功")
                        
                    except Exception as e:
                        connection_status = f"连接失败: {str(e)}"
                        print(f"连接失败: {e}")
                
                # 使用统一的事件循环
                future = run_async_task(connect())
                future.result(timeout=10)  # 等待连接完成，最多10秒
                
            except Exception as e:
                connection_status = f"连接错误: {str(e)}"
                print(f"连接错误: {e}")
        
        # 在新线程中连接
        thread = threading.Thread(target=connect_websocket)
        thread.daemon = True
        thread.start()
        
        self.report({'INFO'}, "正在连接服务器...")
        return {'FINISHED'}

class MOTIONSTREAMER_OT_disconnect(Operator):
    """断开服务器连接"""
    bl_idname = "motionstreamer.disconnect"
    bl_label = "断开连接"
    bl_description = "断开与MotionStreamer服务器的连接"
    
    def execute(self, context):
        global websocket_connection, connection_status
        
        if websocket_connection:
            try:
                # 使用统一的事件循环关闭连接
                async def close_connection():
                    global websocket_connection
                    if websocket_connection:
                        await websocket_connection.close()
                
                future = run_async_task(close_connection())
                future.result(timeout=5)  # 等待关闭完成，最多5秒
                
                websocket_connection = None
                connection_status = "未连接"
                self.report({'INFO'}, "已断开服务器连接")
            except Exception as e:
                self.report({'ERROR'}, f"断开连接时出错: {e}")
        else:
            self.report({'INFO'}, "未连接到服务器")
        
        return {'FINISHED'}

class MOTIONSTREAMER_OT_generate_motion(Operator):
    """生成动作"""
    bl_idname = "motionstreamer.generate_motion"
    bl_label = "生成动作"
    bl_description = "根据文本描述生成动作"
    
    def execute(self, context):
        if not WEBSOCKETS_AVAILABLE:
            self.report({'ERROR'}, "WebSocket功能不可用，请安装websockets库")
            return {'CANCELLED'}
            
        global websocket_connection, motion_data_cache
        
        if not websocket_connection:
            self.report({'ERROR'}, "请先连接到服务器")
            return {'CANCELLED'}
        
        props = context.scene.motionstreamer_props
        
        if not props.motion_text.strip():
            self.report({'ERROR'}, "请输入动作描述文本")
            return {'CANCELLED'}
        
        def generate_motion():
            global motion_data_cache
            
            # 清空之前的动作数据缓存
            motion_data_cache = []
            
            try:
                async def send_request():
                    global motion_data_cache, frame_count, last_frame_time, frame_rate_samples
                    
                    request = {
                        "command": "generate_motion",
                        "text": props.motion_text,
                        "mode": props.motion_mode,
                        "streaming": True  # 启用流式生成
                    }
                    
                    await websocket_connection.send(json.dumps(request))
                    
                    # 等待服务器响应，可能会收到多个消息
                    while True:
                        response = await websocket_connection.recv()
                        data = json.loads(response)
                        
                        if data.get('status') == 'streaming_start':
                            print(f"服务器状态: {data.get('message', '正在生成...')}")
                            continue  # 继续等待更多消息
                        elif data.get('status') == 'streaming_frames':
                            # 处理流式生成帧数据更新
                            
                            current_token = data.get('current_token', 0)
                            progress_percent = data.get('progress_percent', 0)
                            new_frames = data.get('new_frames', [])
                            total_frames = data.get('total_frames', 0)
                            first_frame_time = data.get('first_frame_time')
                            
                            # 性能监控：计算帧率
                            current_time = time.time()
                            if last_frame_time > 0:
                                frame_interval = current_time - last_frame_time
                                if frame_interval > 0:
                                    frame_rate = len(new_frames) / frame_interval
                                    frame_rate_samples.append(frame_rate)
                                    # 保持最近10个样本
                                    if len(frame_rate_samples) > 10:
                                        frame_rate_samples.pop(0)
                                    avg_frame_rate = sum(frame_rate_samples) / len(frame_rate_samples)
                                else:
                                    avg_frame_rate = 0
                            else:
                                avg_frame_rate = 0
                            last_frame_time = current_time
                            frame_count += len(new_frames)
                            
                            # 显示首帧耗时信息和性能信息
                            first_frame_info = f" (首帧耗时: {first_frame_time:.3f}s)" if first_frame_time else ""
                            perf_info = f" [帧率: {avg_frame_rate:.1f} fps]" if avg_frame_rate > 0 else ""
                            print(f"接收到新帧数据: +{len(new_frames)} 帧, 总计: {total_frames} 帧, token: {current_token}, 进度: {progress_percent:.1f}%{first_frame_info}{perf_info}")
                            
                            # 实时更新动作数据缓存
                            if new_frames:
                                motion_data_cache.extend(new_frames)

                                # 调试：打印接收到的数据样本
                                if len(new_frames) > 0:
                                    sample_frame = new_frames[0]
                                    print(f"📊 接收到的帧数据样本:")
                                    print(f"   - 帧数据类型: {type(sample_frame)}")
                                    print(f"   - 帧数据长度: {len(sample_frame)}")
                                    if len(sample_frame) > 0:
                                        print(f"   - 第一个元素: {sample_frame[0]} (类型: {type(sample_frame[0])})")
                                        if len(sample_frame) > 1:
                                            print(f"   - 第二个元素: {sample_frame[1]}")
                                        if len(sample_frame) > 2:
                                            print(f"   - 第三个元素: {sample_frame[2]}")

                                    print(f"   - 当前缓存总帧数: {len(motion_data_cache)}")

                            continue  # 继续等待更多消息
                        elif data.get('status') == 'streaming_complete':
                            # 流式生成完成，获取最终结果
                            motion_data_cache = data.get('motion_data', [])
                            frames = data.get('frames', len(motion_data_cache))
                            total_time = data.get('total_time', 0)
                            first_frame_time = data.get('first_frame_time')

                            # 调试：打印最终数据信息
                            print(f"🎯 流式生成完成，最终数据分析:")
                            print(f"   - 总帧数: {len(motion_data_cache)}")
                            if motion_data_cache:
                                final_sample = motion_data_cache[0]
                                print(f"   - 最终数据格式: {type(final_sample)}")
                                print(f"   - 第一帧长度: {len(final_sample)}")
                                if len(final_sample) > 0:
                                    print(f"   - 第一帧前3个元素: {final_sample[:3]}")

                            # 计算整体性能统计
                            overall_frame_rate = frame_count / total_time if total_time > 0 else 0
                            avg_frame_rate = sum(frame_rate_samples) / len(frame_rate_samples) if frame_rate_samples else 0

                            # 显示完成信息，包括首帧耗时和性能统计
                            first_frame_info = f" (首帧耗时: {first_frame_time:.3f}s)" if first_frame_time else ""
                            perf_info = f" [平均帧率: {avg_frame_rate:.1f} fps, 整体帧率: {overall_frame_rate:.1f} fps]"
                            print(f"动作生成成功: {frames} 帧 (耗时: {total_time:.2f}秒){first_frame_info}{perf_info}")

                            # 重置性能监控变量
                            frame_count = 0
                            last_frame_time = 0
                            frame_rate_samples = []

                            # 在主线程中应用动作
                            bpy.app.timers.register(lambda: apply_motion_to_armature(context))
                            break
                        elif data.get('status') == 'streaming_error':
                            print(f"流式生成失败: {data.get('message', '未知错误')}")
                            break
                        elif data.get('status') == 'success':
                            # 兼容非流式模式的成功响应
                            motion_data_cache = data.get('motion_data', [])
                            print(f"动作生成成功: {len(motion_data_cache)} 帧")
                            
                            # 在主线程中应用动作
                            bpy.app.timers.register(lambda: apply_motion_to_armature(context))
                            break
                        elif data.get('status') == 'error':
                            print(f"动作生成失败: {data.get('message', '未知错误')}")
                            break
                        else:
                            print(f"收到未知状态: {data.get('status')} - {data}")
                            # 对于未知状态，继续等待而不是直接退出
                            continue
                
                # 使用统一的事件循环
                future = run_async_task(send_request())
                future.result(timeout=60)  # 增加超时时间到60秒
                
            except Exception as e:
                print(f"生成动作时出错: {e}")
        
        # 在新线程中生成动作
        thread = threading.Thread(target=generate_motion)
        thread.daemon = True
        thread.start()
        
        self.report({'INFO'}, f"正在生成动作: {props.motion_text}")
        return {'FINISHED'}

def apply_motion_to_armature(context):
    """将动作应用到骨架"""
    global motion_data_cache

    print("=" * 60)
    print("MotionStreamer: 开始应用动作到骨架")
    print("=" * 60)

    if not motion_data_cache:
        print("❌ MotionStreamer: 没有动作数据缓存")
        return None

    # 详细打印动作数据信息
    print(f"✅ 动作数据缓存状态:")
    print(f"   - 帧数: {len(motion_data_cache)}")
    if motion_data_cache:
        first_frame = motion_data_cache[0]
        print(f"   - 第一帧数据类型: {type(first_frame)}")
        print(f"   - 第一帧数据长度: {len(first_frame)}")

        # 打印前几帧的部分数据
        print(f"   - 前3帧的前5个数据点:")
        for i in range(min(3, len(motion_data_cache))):
            frame_data = motion_data_cache[i]
            if isinstance(frame_data, list) and len(frame_data) > 0:
                sample_data = frame_data[:5]  # 只显示前5个数据点
                print(f"     帧{i}: {sample_data}")
            else:
                print(f"     帧{i}: 数据格式异常 - {type(frame_data)}")

    if not mmd_driver:
        print("❌ MotionStreamer: MMD驱动模块未加载")
        return None

    props = context.scene.motionstreamer_props
    print(f"✅ 插件属性:")
    print(f"   - 目标骨架: '{props.target_armature}'")
    print(f"   - 动作模式: '{props.motion_mode}'")
    print(f"   - 自动播放: {props.auto_play}")

    # 获取MMD驱动器实例
    driver = mmd_driver.get_mmd_driver()
    print(f"✅ MMD驱动器实例已获取")

    # 查找目标骨架
    armature = driver.find_armature(props.target_armature)

    if not armature:
        print("❌ MotionStreamer: 未找到目标骨架")
        print("   可用的骨架对象:")
        for obj in bpy.data.objects:
            if obj.type == 'ARMATURE':
                print(f"     - {obj.name} (类型: {obj.type})")
        return None

    print(f"✅ 找到目标骨架: {armature.name}")
    print(f"   - 骨架类型: {armature.type}")
    print(f"   - 骨骼数量: {len(armature.pose.bones) if armature.pose else 0}")

    # 打印前几个骨骼名称
    if armature.pose and armature.pose.bones:
        print(f"   - 前10个骨骼名称:")
        for i, bone in enumerate(list(armature.pose.bones)[:10]):
            print(f"     {i}: {bone.name}")

    # 应用动作数据
    try:
        print(f"🔄 开始应用动作数据...")
        success = driver.apply_motion_data(armature, motion_data_cache, props.motion_mode)

        if success:
            print(f"✅ 动作数据应用成功!")
            print(f"   - 动画帧范围: {bpy.context.scene.frame_start} - {bpy.context.scene.frame_end}")

            if props.auto_play:
                print(f"🎬 开始自动播放动画...")
                bpy.ops.screen.animation_play()
            else:
                print(f"⏸️  动画已准备就绪，请手动播放")

            print(f"✅ MotionStreamer: 动作已成功应用到骨架: {armature.name}")
        else:
            print("❌ MotionStreamer: 动作应用失败")

    except Exception as e:
        print(f"❌ MotionStreamer: 应用动作时出错: {e}")
        import traceback
        traceback.print_exc()

    print("=" * 60)
    print("MotionStreamer: 动作应用流程结束")
    print("=" * 60)

    return None



class MOTIONSTREAMER_OT_play_animation(Operator):
    """播放动画"""
    bl_idname = "motionstreamer.play_animation"
    bl_label = "播放动画"
    bl_description = "播放生成的动作动画"

    def execute(self, context):
        global motion_data_cache

        if not motion_data_cache:
            self.report({'ERROR'}, "没有可播放的动作数据")
            return {'CANCELLED'}

        bpy.ops.screen.animation_play()
        self.report({'INFO'}, "开始播放动画")
        return {'FINISHED'}

class MOTIONSTREAMER_OT_stop_animation(Operator):
    """停止动画"""
    bl_idname = "motionstreamer.stop_animation"
    bl_label = "停止动画"
    bl_description = "停止播放动画"

    def execute(self, context):
        bpy.ops.screen.animation_cancel()
        self.report({'INFO'}, "动画已停止")
        return {'FINISHED'}

class MOTIONSTREAMER_PT_main_panel(Panel):
    """MotionStreamer主面板"""
    bl_label = "MotionStreamer"
    bl_idname = "MOTIONSTREAMER_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "MotionStreamer"

    def draw(self, context):
        layout = self.layout
        props = context.scene.motionstreamer_props

        # 连接状态显示
        box = layout.box()
        box.label(text="服务器连接", icon='NETWORK_DRIVE')

        row = box.row()
        row.prop(props, "server_host")
        row.prop(props, "server_port")

        # 连接按钮和状态显示
        row = box.row()
        if WEBSOCKETS_AVAILABLE:
            if websocket_connection:
                row.operator("motionstreamer.disconnect", icon='CANCEL')
                row.label(text=connection_status, icon='CHECKMARK')
            else:
                row.operator("motionstreamer.connect", icon='PLAY')
                row.label(text=connection_status, icon='ERROR')
        else:
            row.label(text="WebSocket功能不可用", icon='ERROR')
            row.label(text="请安装websockets库", icon='INFO')

class MOTIONSTREAMER_PT_motion_panel(Panel):
    """动作生成面板"""
    bl_label = "动作生成"
    bl_idname = "MOTIONSTREAMER_PT_motion_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "MotionStreamer"
    bl_parent_id = "MOTIONSTREAMER_PT_main_panel"

    def draw(self, context):
        layout = self.layout
        props = context.scene.motionstreamer_props

        # 文本输入
        box = layout.box()
        box.label(text="动作描述", icon='TEXT')
        box.prop(props, "motion_text", text="")
        box.prop(props, "motion_mode")

        # 生成按钮
        row = box.row()
        row.scale_y = 1.5
        row.operator("motionstreamer.generate_motion", icon='PLAY')

        # 显示动作数据信息
        if motion_data_cache:
            info_box = layout.box()
            info_box.label(text="动作信息", icon='INFO')
            info_box.label(text=f"帧数: {len(motion_data_cache)}")
            if motion_data_cache:
                info_box.label(text=f"关节数: {len(motion_data_cache[0])}")

class MOTIONSTREAMER_PT_control_panel(Panel):
    """播放控制面板"""
    bl_label = "播放控制"
    bl_idname = "MOTIONSTREAMER_PT_control_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "MotionStreamer"
    bl_parent_id = "MOTIONSTREAMER_PT_main_panel"

    def draw(self, context):
        layout = self.layout
        props = context.scene.motionstreamer_props

        # 目标设置
        box = layout.box()
        box.label(text="目标设置", icon='ARMATURE_DATA')
        box.prop_search(props, "target_armature", bpy.data, "objects")

        # 播放设置
        box = layout.box()
        box.label(text="播放设置", icon='SETTINGS')
        box.prop(props, "auto_play")
        box.prop(props, "playback_speed")
        box.prop(props, "loop_animation")

        # 播放控制按钮
        row = box.row()
        row.operator("motionstreamer.play_animation", icon='PLAY')
        row.operator("motionstreamer.stop_animation", icon='PAUSE')

# 注册类列表
classes = [
    MotionStreamerProperties,
    MOTIONSTREAMER_OT_connect,
    MOTIONSTREAMER_OT_disconnect,
    MOTIONSTREAMER_OT_generate_motion,
    MOTIONSTREAMER_OT_play_animation,
    MOTIONSTREAMER_OT_stop_animation,
    MOTIONSTREAMER_PT_main_panel,
    MOTIONSTREAMER_PT_motion_panel,
    MOTIONSTREAMER_PT_control_panel,
]

def register():
    """注册插件"""
    try:
        print("MotionStreamer: 开始注册插件...")
        for cls in classes:
            print(f"MotionStreamer: 注册类 {cls.__name__}")
            bpy.utils.register_class(cls)
        
        bpy.types.Scene.motionstreamer_props = bpy.props.PointerProperty(type=MotionStreamerProperties)
        print("MotionStreamer: 插件注册成功！")
    except Exception as e:
        print(f"MotionStreamer: 插件注册失败 - {e}")
        import traceback
        traceback.print_exc()

def unregister():
    """注销插件"""
    global event_loop, loop_thread
    
    try:
        print("MotionStreamer: 开始注销插件...")
        
        # 停止事件循环
        if event_loop and not event_loop.is_closed():
            event_loop.call_soon_threadsafe(event_loop.stop)
        
        for cls in reversed(classes):
            bpy.utils.unregister_class(cls)
        
        if hasattr(bpy.types.Scene, 'motionstreamer_props'):
            del bpy.types.Scene.motionstreamer_props
        print("MotionStreamer: 插件注销成功！")
    except Exception as e:
        print(f"MotionStreamer: 插件注销失败 - {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("MotionStreamer: 插件加载--------------------")
    register()

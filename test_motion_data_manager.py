"""
测试动作数据管理模块
"""

import sys
import os
import json
import numpy as np

# 添加blender_addon目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'blender_addon'))

from motion_data_manager import MotionDataManager

def create_test_motion_data():
    """创建测试动作数据"""
    frames = 60
    joints = 22
    motion_data = []
    
    for frame in range(frames):
        frame_data = []
        
        for joint in range(joints):
            t = frame / frames * 2 * np.pi
            
            if joint == 0:  # 根关节
                x = frame * 0.02
                y = 0.0
                z = np.sin(t * 2) * 0.05
            elif joint in [16, 19]:  # 腿部
                x = 0.0
                y = np.sin(t + (np.pi if joint == 19 else 0)) * 0.1
                z = 0.0
            elif joint in [9, 13]:  # 手臂
                x = np.sin(t + (np.pi if joint == 13 else 0)) * 0.08
                y = 0.0
                z = 0.0
            else:
                x = np.sin(t) * 0.01
                y = 0.0
                z = 0.0
            
            frame_data.append([x, y, z])
        
        motion_data.append(frame_data)
    
    return motion_data

def test_motion_data_manager():
    """测试动作数据管理器"""
    print("=" * 60)
    print("测试动作数据管理器")
    print("=" * 60)
    
    # 创建管理器实例
    manager = MotionDataManager()
    print(f"✅ 创建管理器成功，数据目录: {manager.get_data_dir()}")
    
    # 创建测试数据
    print("\n📊 创建测试动作数据...")
    motion_data = create_test_motion_data()
    print(f"   - 帧数: {len(motion_data)}")
    print(f"   - 关节数: {len(motion_data[0])}")
    
    # 保存测试数据
    print("\n💾 保存动作数据...")
    try:
        saved_file = manager.save_motion_data(
            motion_data=motion_data,
            text="测试行走动作",
            mode="pos",
            metadata={
                "test": True,
                "description": "这是一个测试动作数据"
            }
        )
        print(f"✅ 保存成功: {saved_file}")
    except Exception as e:
        print(f"❌ 保存失败: {e}")
        return
    
    # 列出保存的动作
    print("\n📋 列出保存的动作...")
    motions = manager.list_saved_motions()
    print(f"   找到 {len(motions)} 个保存的动作:")
    for i, motion in enumerate(motions):
        print(f"   {i+1}. {motion['text']} ({motion['mode']}) - {motion['frames']} 帧")
        print(f"      文件: {motion['filename']}")
        print(f"      时间: {motion['timestamp']}")
        print(f"      大小: {motion['file_size'] / 1024:.1f} KB")
    
    # 加载动作数据
    if motions:
        print(f"\n📂 加载第一个动作数据...")
        try:
            loaded_data = manager.load_motion_data(motions[0]['filepath'])
            if loaded_data:
                print(f"✅ 加载成功:")
                print(f"   - 描述: {loaded_data['text']}")
                print(f"   - 模式: {loaded_data['mode']}")
                print(f"   - 帧数: {loaded_data['frames']}")
                print(f"   - 关节数: {loaded_data['joints']}")
                print(f"   - 元数据: {loaded_data.get('metadata', {})}")
            else:
                print("❌ 加载失败")
        except Exception as e:
            print(f"❌ 加载出错: {e}")
    
    # 获取存储信息
    print(f"\n📊 存储信息:")
    storage_info = manager.get_storage_info()
    for key, value in storage_info.items():
        if key == 'total_size':
            print(f"   {key}: {value / 1024:.1f} KB")
        else:
            print(f"   {key}: {value}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_motion_data_manager()

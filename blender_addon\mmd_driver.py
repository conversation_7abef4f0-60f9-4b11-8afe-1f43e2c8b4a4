"""
MMD模型驱动模块
负责将动作数据应用到Blender中的MMD模型
"""

import bpy
import mathutils
import numpy as np
import time
from mathutils import Vector, Euler, Quaternion, Matrix


class MMDDriver:
    """MMD模型驱动器"""
    
    def __init__(self):
        # HumanML3D的22个关节到MMD骨骼的映射
        self.joint_to_mmd_mapping = {
            0: "センター",          # pelvis/root
            1: "下半身",            # left_hip (用下半身代替)
            2: "下半身",            # right_hip (用下半身代替)
            3: "上半身",            # spine1
            4: "上半身2",           # spine2
            5: "首",               # spine3/neck
            6: "首",               # neck
            7: "頭",               # head
            8: "左肩",             # left_collar
            9: "左腕",             # left_shoulder
            10: "左ひじ",          # left_elbow
            11: "左手首",          # left_wrist
            12: "右肩",            # right_collar
            13: "右腕",            # right_shoulder
            14: "右ひじ",          # right_elbow
            15: "右手首",          # right_wrist
            16: "左足",            # left_hip
            17: "左ひざ",          # left_knee
            18: "左足首",          # left_ankle
            19: "右足",            # right_hip
            20: "右ひざ",          # right_knee
            21: "右足首",          # right_ankle
        }
        
        # 备用骨骼名称映射（适应不同的MMD模型）
        self.alternative_bone_names = {
            "センター": ["center", "Center", "root", "Root"],
            "下半身": ["lower body", "Lower Body", "hip", "Hip"],
            "上半身": ["upper body", "Upper Body", "spine", "Spine"],
            "上半身2": ["upper body2", "Upper Body2", "spine1", "Spine1"],
            "首": ["neck", "Neck"],
            "頭": ["head", "Head"],
            "左肩": ["left shoulder", "Left Shoulder", "shoulder_L", "Shoulder_L"],
            "左腕": ["left arm", "Left Arm", "arm_L", "Arm_L"],
            "左ひじ": ["left elbow", "Left Elbow", "elbow_L", "Elbow_L"],
            "左手首": ["left wrist", "Left Wrist", "wrist_L", "Wrist_L"],
            "右肩": ["right shoulder", "Right Shoulder", "shoulder_R", "Shoulder_R"],
            "右腕": ["right arm", "Right Arm", "arm_R", "Arm_R"],
            "右ひじ": ["right elbow", "Right Elbow", "elbow_R", "Elbow_R"],
            "右手首": ["right wrist", "Right Wrist", "wrist_R", "Wrist_R"],
            "左足": ["left leg", "Left Leg", "leg_L", "Leg_L"],
            "左ひざ": ["left knee", "Left Knee", "knee_L", "Knee_L"],
            "左足首": ["left ankle", "Left Ankle", "ankle_L", "Ankle_L"],
            "右足": ["right leg", "Right Leg", "leg_R", "Leg_R"],
            "右ひざ": ["right knee", "Right Knee", "knee_R", "Knee_R"],
            "右足首": ["right ankle", "Right Ankle", "ankle_R", "Ankle_R"],
        }
    
    def find_armature(self, target_name=None):
        """查找目标骨架对象"""
        if target_name:
            armature = bpy.data.objects.get(target_name)
            if armature and armature.type == 'ARMATURE':
                return armature
        
        # 自动查找第一个骨架对象
        for obj in bpy.data.objects:
            if obj.type == 'ARMATURE':
                return obj
        
        return None
    
    def find_bone_name(self, armature, target_bone_name):
        """查找实际存在的骨骼名称"""
        if not armature or not armature.pose:
            return None

        # 首先尝试直接匹配
        if target_bone_name in armature.pose.bones:
            return target_bone_name

        # 尝试备用名称
        alternatives = self.alternative_bone_names.get(target_bone_name, [])
        for alt_name in alternatives:
            if alt_name in armature.pose.bones:
                return alt_name

        return None

    def debug_armature_bones(self, armature):
        """调试输出骨架的所有骨骼信息"""
        print(f"\n🦴 骨架调试信息: {armature.name}")

        if not armature.pose:
            print("   ❌ 骨架没有姿态数据")
            return

        bones = list(armature.pose.bones)
        print(f"   📊 总骨骼数: {len(bones)}")

        # 按名称分组显示骨骼
        bone_groups = {
            '中心/根部': [],
            '身体': [],
            '头部': [],
            '左臂': [],
            '右臂': [],
            '左腿': [],
            '右腿': [],
            '其他': []
        }

        for bone in bones:
            name = bone.name
            if any(keyword in name for keyword in ['センター', 'center', 'root', 'Center', 'Root']):
                bone_groups['中心/根部'].append(name)
            elif any(keyword in name for keyword in ['上半身', '下半身', 'body', 'Body', 'spine', 'Spine']):
                bone_groups['身体'].append(name)
            elif any(keyword in name for keyword in ['頭', '首', 'head', 'Head', 'neck', 'Neck']):
                bone_groups['头部'].append(name)
            elif any(keyword in name for keyword in ['左', 'left', 'Left', '_L', '.L']):
                if any(keyword in name for keyword in ['腕', '肩', 'arm', 'shoulder', 'elbow', 'wrist']):
                    bone_groups['左臂'].append(name)
                else:
                    bone_groups['左腿'].append(name)
            elif any(keyword in name for keyword in ['右', 'right', 'Right', '_R', '.R']):
                if any(keyword in name for keyword in ['腕', '肩', 'arm', 'shoulder', 'elbow', 'wrist']):
                    bone_groups['右臂'].append(name)
                else:
                    bone_groups['右腿'].append(name)
            else:
                bone_groups['其他'].append(name)

        for group_name, bone_list in bone_groups.items():
            if bone_list:
                print(f"   📂 {group_name} ({len(bone_list)}个):")
                for bone_name in bone_list[:5]:  # 只显示前5个
                    print(f"      - {bone_name}")
                if len(bone_list) > 5:
                    print(f"      ... 还有{len(bone_list)-5}个")

        # 检查我们需要的关键骨骼
        print(f"\n🎯 关键骨骼映射检查:")
        key_joints = [0, 7, 9, 13, 16, 19]  # 根、头、左右臂、左右腿
        for joint_idx in key_joints:
            if joint_idx in self.joint_to_mmd_mapping:
                target_name = self.joint_to_mmd_mapping[joint_idx]
                found_name = self.find_bone_name(armature, target_name)
                status = "✅" if found_name else "❌"
                print(f"   {status} 关节{joint_idx}: {target_name} -> {found_name or '未找到'}")

        return bone_groups
    
    def apply_motion_data(self, armature, motion_data, mode='pos'):
        """将动作数据应用到骨架对象"""
        print("\n" + "="*50)
        print("MMDDriver: 开始应用动作数据")
        print("="*50)

        if not motion_data or not armature:
            print("❌ MMDDriver: 没有动作数据或骨架对象")
            print(f"   - motion_data: {motion_data is not None}")
            print(f"   - armature: {armature is not None}")
            return False

        print(f"✅ 输入验证通过:")
        print(f"   - 骨架名称: {armature.name}")
        print(f"   - 动作模式: {mode}")
        print(f"   - 动作数据帧数: {len(motion_data)}")

        try:
            # 设置动画帧范围
            frame_count = len(motion_data)
            old_start = bpy.context.scene.frame_start
            old_end = bpy.context.scene.frame_end

            bpy.context.scene.frame_start = 1
            bpy.context.scene.frame_end = frame_count

            print(f"✅ 动画帧范围设置:")
            print(f"   - 原始范围: {old_start} - {old_end}")
            print(f"   - 新范围: 1 - {frame_count}")

            # 选择并激活骨架
            old_active = bpy.context.view_layer.objects.active
            old_mode = bpy.context.object.mode if bpy.context.object else None

            bpy.context.view_layer.objects.active = armature
            print(f"✅ 激活骨架: {armature.name}")

            # 确保进入姿态模式
            if bpy.context.object and bpy.context.object.type == 'ARMATURE':
                if bpy.context.mode != 'POSE':
                    bpy.ops.object.mode_set(mode='POSE')
                    print(f"✅ 切换到姿态模式")
                else:
                    print(f"✅ 已在姿态模式")

            # 清除现有动画数据
            if armature.animation_data:
                print(f"🗑️  清除现有动画数据")
                armature.animation_data_clear()
            else:
                print(f"ℹ️  没有现有动画数据需要清除")

            # 创建新的动作
            action_name = f"MotionStreamer_{int(time.time())}"
            action = bpy.data.actions.new(name=action_name)
            armature.animation_data_create()
            armature.animation_data.action = action

            print(f"✅ 创建新动作: {action_name}")
            print(f"   - 动作对象: {action}")
            print(f"   - 动画数据: {armature.animation_data}")

            # 调试骨架信息
            self.debug_armature_bones(armature)

            # 根据模式应用数据
            if mode == 'pos':
                print(f"🔄 开始应用位置数据...")
                result = self._apply_position_data(armature, motion_data)
            else:
                print(f"🔄 开始应用旋转数据...")
                result = self._apply_rotation_data(armature, motion_data)

            print(f"✅ 数据应用完成，结果: {result}")
            return result

        except Exception as e:
            print(f"❌ MMDDriver: 应用动作数据时出错 - {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _apply_position_data(self, armature, motion_data):
        """应用位置数据到骨架"""
        print(f"\n📊 MMDDriver: 开始分析位置数据")
        print(f"   - 总帧数: {len(motion_data)}")

        # 获取第一帧数据来确定数据格式
        if not motion_data or len(motion_data[0]) == 0:
            print("❌ MMDDriver: 动作数据为空")
            return False

        first_frame = motion_data[0]
        joints_count = len(first_frame)

        print(f"   - 第一帧数据长度: {joints_count}")
        print(f"   - 第一帧数据类型: {type(first_frame)}")

        # 详细分析数据结构
        if joints_count > 0:
            first_element = first_frame[0]
            print(f"   - 第一个元素类型: {type(first_element)}")
            print(f"   - 第一个元素值: {first_element}")

            if isinstance(first_element, (list, tuple)):
                print(f"   - 第一个元素长度: {len(first_element)}")
                print(f"   - 数据结构: 嵌套列表 (可能是3D坐标)")
            else:
                print(f"   - 数据结构: 平坦列表")

        # 打印前几个数据点作为样本
        print(f"   - 前5个数据点样本:")
        for i in range(min(5, joints_count)):
            print(f"     [{i}]: {first_frame[i]}")

        print(f"🔍 MMDDriver: 数据格式检测")

        # 如果是22个关节的3D位置数据 (每个关节是[x,y,z])
        if joints_count == 22 and isinstance(first_frame[0], (list, tuple)) and len(first_frame[0]) == 3:
            print(f"✅ 检测到格式: 22个关节的3D位置数据")
            return self._apply_joint_positions_3d(armature, motion_data)
        # 如果是66维展平数据 (22*3=66)
        elif joints_count == 66:
            print(f"✅ 检测到格式: 66维展平位置数据")
            return self._apply_joint_positions(armature, motion_data)
        else:
            print(f"❌ MMDDriver: 不支持的数据格式")
            print(f"   - 期望格式1: 22个关节，每个关节3个坐标 [[x,y,z], [x,y,z], ...]")
            print(f"   - 期望格式2: 66维展平数据 [x,y,z,x,y,z,...]")
            print(f"   - 实际格式: {joints_count}个元素，第一个元素类型: {type(first_frame[0])}")
            return False
    
    def _apply_joint_positions_3d(self, armature, motion_data):
        """应用3D关节位置数据"""
        print(f"\n🎯 开始应用3D关节位置数据")

        applied_bones = 0
        failed_bones = 0
        bone_mapping_results = {}

        # 首先分析骨骼映射情况
        print(f"🔍 分析骨骼映射:")
        for joint_idx in range(22):
            if joint_idx in self.joint_to_mmd_mapping:
                target_bone_name = self.joint_to_mmd_mapping[joint_idx]
                actual_bone_name = self.find_bone_name(armature, target_bone_name)

                if actual_bone_name:
                    bone_mapping_results[joint_idx] = {
                        'target': target_bone_name,
                        'actual': actual_bone_name,
                        'found': True
                    }
                    print(f"   ✅ 关节{joint_idx}: {target_bone_name} -> {actual_bone_name}")
                else:
                    bone_mapping_results[joint_idx] = {
                        'target': target_bone_name,
                        'actual': None,
                        'found': False
                    }
                    print(f"   ❌ 关节{joint_idx}: {target_bone_name} -> 未找到")

        print(f"\n🎬 开始逐帧应用动作数据:")

        for frame_idx, frame_data in enumerate(motion_data):
            bpy.context.scene.frame_set(frame_idx + 1)

            # 只在前几帧和关键帧打印详细信息
            debug_this_frame = frame_idx < 3 or frame_idx % 20 == 0

            if debug_this_frame:
                print(f"   📍 处理第{frame_idx + 1}帧:")

            for joint_idx, position in enumerate(frame_data):
                if joint_idx in bone_mapping_results and bone_mapping_results[joint_idx]['found']:
                    actual_bone_name = bone_mapping_results[joint_idx]['actual']
                    bone = armature.pose.bones[actual_bone_name]

                    # 转换坐标系：HumanML3D使用Y-up，Blender使用Z-up
                    original_pos = Vector(position)
                    blender_pos = Vector((
                        position[0],    # X保持不变
                        -position[2],   # Z变为-Y (前后翻转)
                        position[1]     # Y变为Z
                    ))

                    # 应用缩放因子
                    scale_factor = 0.01 if joint_idx == 0 else 0.001  # 根骨骼缩放更大
                    blender_pos *= scale_factor

                    # 设置骨骼位置
                    old_location = bone.location.copy()
                    bone.location = blender_pos
                    bone.keyframe_insert(data_path="location")

                    if debug_this_frame and joint_idx in [0, 7, 9, 13, 16, 19]:  # 只显示重要关节
                        print(f"     关节{joint_idx}({actual_bone_name}):")
                        print(f"       原始位置: {original_pos}")
                        print(f"       转换位置: {blender_pos}")
                        print(f"       旧位置: {old_location}")

                    if frame_idx == 0:  # 只在第一帧计数
                        applied_bones += 1
                else:
                    if frame_idx == 0:  # 只在第一帧计数失败的骨骼
                        failed_bones += 1

        print(f"\n📊 应用结果统计:")
        print(f"   ✅ 成功应用的骨骼数: {applied_bones}")
        print(f"   ❌ 失败的骨骼数: {failed_bones}")
        print(f"   📈 成功率: {applied_bones/(applied_bones+failed_bones)*100:.1f}%")

        # 检查是否有任何关键帧被创建
        if armature.animation_data and armature.animation_data.action:
            fcurves = armature.animation_data.action.fcurves
            print(f"   🎞️  创建的F-Curve数量: {len(fcurves)}")

            if len(fcurves) > 0:
                print(f"   📋 前几个F-Curve:")
                for i, fcurve in enumerate(fcurves[:5]):
                    print(f"     {i}: {fcurve.data_path} [{fcurve.array_index}] - {len(fcurve.keyframe_points)}个关键帧")

        success = applied_bones > 0
        print(f"   🎯 最终结果: {'成功' if success else '失败'}")

        return success
    
    def _apply_joint_positions(self, armature, motion_data):
        """应用展平的关节位置数据 (66维)"""
        applied_bones = 0
        
        for frame_idx, frame_data in enumerate(motion_data):
            bpy.context.scene.frame_set(frame_idx + 1)
            
            # 将66维数据重新整形为22个关节的3D位置
            positions = np.array(frame_data).reshape(22, 3)
            
            for joint_idx in range(22):
                if joint_idx in self.joint_to_mmd_mapping:
                    target_bone_name = self.joint_to_mmd_mapping[joint_idx]
                    actual_bone_name = self.find_bone_name(armature, target_bone_name)
                    
                    if actual_bone_name:
                        bone = armature.pose.bones[actual_bone_name]
                        position = positions[joint_idx]
                        
                        # 坐标系转换和缩放
                        blender_pos = Vector((
                            position[0],
                            -position[2],
                            position[1]
                        )) * 0.01
                        
                        bone.location = blender_pos
                        bone.keyframe_insert(data_path="location")
                        
                        if frame_idx == 0:
                            applied_bones += 1
        
        print(f"MMDDriver: 成功应用了{applied_bones}个骨骼的动画")
        return applied_bones > 0
    
    def _apply_rotation_data(self, armature, motion_data):
        """应用旋转数据到骨架（272维数据）"""
        print(f"MMDDriver: 开始应用旋转数据，共{len(motion_data)}帧")
        
        applied_bones = 0
        
        for frame_idx, frame_data in enumerate(motion_data):
            bpy.context.scene.frame_set(frame_idx + 1)
            
            # 简化处理：只处理根骨骼的移动
            center_bone_name = self.find_bone_name(armature, "センター")
            if center_bone_name:
                root_bone = armature.pose.bones[center_bone_name]
                
                # 从272维数据中提取根骨骼的位置信息
                if len(frame_data) >= 2:
                    # 前两维是根骨骼的XZ速度
                    root_bone.location.x = frame_data[0] * 0.01
                    root_bone.location.y = frame_data[1] * 0.01 if len(frame_data) > 1 else 0
                    root_bone.keyframe_insert(data_path="location")
                    
                    if frame_idx == 0:
                        applied_bones += 1
        
        print(f"MMDDriver: 成功应用了{applied_bones}个骨骼的动画")
        return applied_bones > 0


# 全局驱动器实例
_mmd_driver = None

def get_mmd_driver():
    """获取MMD驱动器实例"""
    global _mmd_driver
    if _mmd_driver is None:
        _mmd_driver = MMDDriver()
    return _mmd_driver

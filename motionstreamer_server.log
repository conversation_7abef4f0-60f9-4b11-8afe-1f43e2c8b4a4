2025-09-29 12:40:07,634 - INFO - 正在初始化模型...
2025-09-29 12:40:07,657 - INFO - Use pytorch device_name: cuda
2025-09-29 12:40:07,658 - INFO - <PERSON>ad pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 12:42:44,416 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 12:42:48,885 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-29 12:43:03,031 - INFO - 模型初始化完成! 耗时: 175.40秒
2025-09-29 12:43:03,031 - INFO - 使用设备: cuda
2025-09-29 12:43:03,085 - INFO - server listening on [::1]:8765
2025-09-29 12:43:03,085 - INFO - server listening on 127.0.0.1:8765
2025-09-29 13:18:17,592 - INFO - connection open
2025-09-29 13:18:17,592 - ERROR - connection handler failed
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: MotionStreamerServer.handle_client() missing 1 required positional argument: 'path'
2025-09-29 13:21:45,390 - INFO - connection open
2025-09-29 13:21:45,390 - ERROR - connection handler failed
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
          ^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: MotionStreamerServer.handle_client() missing 1 required positional argument: 'path'
2025-09-29 13:45:55,513 - INFO - 正在初始化模型...
2025-09-29 13:45:55,536 - INFO - Use pytorch device_name: cuda
2025-09-29 13:45:55,536 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 13:48:33,257 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 13:48:37,554 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-29 13:48:50,878 - INFO - 模型初始化完成! 耗时: 175.36秒
2025-09-29 13:48:50,879 - INFO - 使用设备: cuda
2025-09-29 13:48:50,910 - INFO - server listening on 127.0.0.1:8765
2025-09-29 13:48:50,911 - INFO - server listening on [::1]:8765
2025-09-29 13:50:14,286 - INFO - connection open
2025-09-29 14:12:10,465 - INFO - 正在初始化模型...
2025-09-29 14:12:10,475 - INFO - Use pytorch device_name: cuda
2025-09-29 14:12:10,476 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 14:13:07,221 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 14:13:11,644 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-29 14:13:25,535 - INFO - 模型初始化完成! 耗时: 75.07秒
2025-09-29 14:13:25,536 - INFO - 使用设备: cuda
2025-09-29 14:13:25,620 - INFO - server listening on 127.0.0.1:8765
2025-09-29 14:13:25,620 - INFO - server listening on [::1]:8765
2025-09-29 14:16:50,595 - INFO - connection open
2025-09-29 14:16:59,990 - INFO - [#1] 正在生成动作: 'A person is walking forward' (模式: pos)
2025-09-29 14:19:08,882 - INFO - [#1] 动作生成完成! 耗时: 128.89秒, 数据: 100 frames, 22 joints, 3D positions
2025-09-29 14:34:46,278 - INFO - 正在初始化模型...
2025-09-29 14:34:46,292 - INFO - Use pytorch device_name: cuda
2025-09-29 14:34:46,292 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 14:36:21,168 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 14:36:45,692 - INFO - 正在初始化模型...
2025-09-29 14:36:45,775 - INFO - Use pytorch device_name: cuda
2025-09-29 14:36:45,775 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 14:37:30,044 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 14:37:34,607 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-29 14:37:48,472 - INFO - 模型初始化完成! 耗时: 62.78秒
2025-09-29 14:37:48,472 - INFO - 使用设备: cuda
2025-09-29 14:38:45,484 - INFO - 正在初始化模型...
2025-09-29 14:38:45,492 - INFO - Use pytorch device_name: cuda
2025-09-29 14:38:45,492 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 14:39:30,946 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 14:39:35,486 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-29 14:39:48,997 - INFO - 模型初始化完成! 耗时: 63.51秒
2025-09-29 14:39:48,997 - INFO - 使用设备: cuda
2025-09-29 14:39:49,083 - INFO - server listening on [::1]:8765
2025-09-29 14:39:49,083 - INFO - server listening on 127.0.0.1:8765
2025-09-29 14:40:53,723 - INFO - connection open
2025-09-29 14:41:05,175 - INFO - [#1] 开始流式生成动作: 'A person is walking forward' (模式: pos)
2025-09-29 14:42:43,776 - ERROR - [#1] 流式生成动作时出错: received 1011 (internal error) keepalive ping timeout; then sent 1011 (internal error) keepalive ping timeout
2025-09-29 14:44:02,015 - INFO - server closing
2025-09-29 14:44:02,015 - INFO - server closed
2025-09-29 14:45:17,382 - INFO - 正在初始化模型...
2025-09-29 14:45:17,390 - INFO - Use pytorch device_name: cuda
2025-09-29 14:45:17,390 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 14:46:13,846 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 14:46:18,256 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-29 14:46:32,066 - INFO - 模型初始化完成! 耗时: 74.68秒
2025-09-29 14:46:32,066 - INFO - 使用设备: cuda
2025-09-29 14:46:32,192 - INFO - server listening on 127.0.0.1:8765
2025-09-29 14:46:32,192 - INFO - server listening on [::1]:8765
2025-09-29 14:47:45,864 - INFO - connection open
2025-09-29 14:47:45,865 - INFO - [#1] 开始流式生成动作: 'a person is walking forward' (模式: pos)
2025-09-29 14:47:57,905 - ERROR - [#1] 流式生成动作时出错: 'ServerConnection' object has no attribute 'closed'
2025-09-29 14:52:36,404 - INFO - 正在初始化模型...
2025-09-29 14:52:36,410 - INFO - Use pytorch device_name: cuda
2025-09-29 14:52:36,410 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 14:53:31,107 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 14:53:35,563 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-29 14:53:49,702 - INFO - 模型初始化完成! 耗时: 73.30秒
2025-09-29 14:53:49,702 - INFO - 使用设备: cuda
2025-09-29 14:53:49,817 - INFO - server listening on 127.0.0.1:8765
2025-09-29 14:53:49,818 - INFO - server listening on [::1]:8765
2025-09-29 14:54:15,428 - INFO - connection open
2025-09-29 14:54:15,429 - INFO - [#1] 开始流式生成动作: 'a person is walking forward' (模式: pos)
2025-09-29 14:55:56,370 - ERROR - 流式生成tokens时出错: unsupported format string passed to Tensor.__format__
2025-09-29 14:55:56,371 - ERROR - [#1] 流式生成动作时出错: unsupported format string passed to Tensor.__format__
2025-09-29 14:55:56,386 - INFO - [#2] 正在生成动作: 'a person is walking forward' (模式: pos)
2025-09-29 14:58:05,603 - INFO - 正在初始化模型...
2025-09-29 14:58:05,690 - INFO - Use pytorch device_name: cuda
2025-09-29 14:58:05,691 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 14:58:48,374 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 14:58:52,531 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-29 14:59:06,102 - INFO - 模型初始化完成! 耗时: 60.50秒
2025-09-29 14:59:06,102 - INFO - 使用设备: cuda
2025-09-29 14:59:06,134 - INFO - server listening on 127.0.0.1:8765
2025-09-29 14:59:06,134 - INFO - server listening on [::1]:8765
2025-09-29 14:59:38,427 - INFO - connection open
2025-09-29 14:59:38,428 - INFO - [#1] 开始流式生成动作: 'a person is walking forward' (模式: pos)
2025-09-29 15:01:27,197 - INFO - 提前停止生成，距离阈值: 0.0125 < 0.1
2025-09-29 15:01:28,920 - INFO - [#1] 流式动作生成完成! 耗时: 110.49秒, 数据: 100 frames, 22 joints, 3D positions
2025-09-29 15:01:28,972 - INFO - [#2] 正在生成动作: 'a person is walking forward' (模式: pos)
2025-09-29 15:04:30,483 - INFO - [#2] 动作生成完成! 耗时: 181.51秒, 数据: 160 frames, 22 joints, 3D positions
2025-09-29 15:04:30,491 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\server.py", line 142, in handshake
    async with self.send_context(expected_state=CONNECTING):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\connection.py", line 957, in send_context
    raise self.protocol.close_exc from original_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-09-29 15:04:30,510 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\server.py", line 142, in handshake
    async with self.send_context(expected_state=CONNECTING):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\connection.py", line 957, in send_context
    raise self.protocol.close_exc from original_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-09-29 15:04:30,511 - ERROR - opening handshake failed
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\server.py", line 356, in conn_handler
    await connection.handshake(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\server.py", line 142, in handshake
    async with self.send_context(expected_state=CONNECTING):
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Python312\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\connection.py", line 957, in send_context
    raise self.protocol.close_exc from original_exc
websockets.exceptions.ConnectionClosedError: no close frame received or sent
2025-09-29 15:06:30,061 - INFO - server closing
2025-09-29 15:06:30,061 - INFO - server closed
2025-09-29 15:06:58,257 - INFO - 正在初始化模型...
2025-09-29 15:06:58,269 - INFO - Use pytorch device_name: cuda
2025-09-29 15:06:58,269 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 15:07:59,136 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 15:08:03,696 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-29 15:08:17,396 - INFO - 模型初始化完成! 耗时: 79.14秒
2025-09-29 15:08:17,396 - INFO - 使用设备: cuda
2025-09-29 15:08:17,481 - INFO - server listening on 127.0.0.1:8765
2025-09-29 15:08:17,481 - INFO - server listening on [::1]:8765
2025-09-29 15:10:48,880 - INFO - connection open
2025-09-29 15:10:55,308 - INFO - [#1] 开始流式生成动作: 'A person is walking forward' (模式: pos)
2025-09-29 15:11:48,621 - WARNING - [#1] WebSocket连接已关闭: received 1000 (OK); then sent 1000 (OK)
2025-09-29 15:11:50,796 - INFO - connection open
2025-09-29 15:12:02,903 - INFO - [#2] 开始流式生成动作: 'A person is walking forward' (模式: pos)
2025-09-29 15:13:21,622 - INFO - 提前停止生成，距离阈值: 0.0136 < 0.1
2025-09-29 15:13:23,530 - INFO - [#2] 流式动作生成完成! 耗时: 80.63秒, 数据: 100 frames, 22 joints, 3D positions
2025-09-29 18:27:02,805 - INFO - 正在初始化模型...
2025-09-29 18:27:02,918 - INFO - Use pytorch device_name: cuda
2025-09-29 18:27:02,918 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 18:28:45,346 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 18:28:49,666 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-29 18:29:03,534 - INFO - 模型初始化完成! 耗时: 120.73秒
2025-09-29 18:29:03,534 - INFO - 使用设备: cuda
2025-09-29 18:29:03,631 - INFO - server listening on [::1]:8765
2025-09-29 18:29:03,631 - INFO - server listening on 127.0.0.1:8765
2025-09-29 18:42:55,230 - INFO - connection open
2025-09-29 18:43:04,595 - INFO - [#1] 开始流式生成动作: 'A person is walking forward' (模式: pos)
2025-09-29 18:43:55,261 - WARNING - [#1] WebSocket连接已关闭: no close frame received or sent
2025-09-29 18:44:32,415 - INFO - connection open
2025-09-29 18:44:44,817 - INFO - [#2] 开始流式生成动作: 'A person is walking forward' (模式: pos)
2025-09-29 18:46:12,456 - WARNING - [#2] WebSocket连接已关闭: received 1011 (internal error) keepalive ping timeout; then sent 1011 (internal error) keepalive ping timeout
2025-09-29 18:47:04,514 - INFO - connection open
2025-09-29 18:47:12,534 - INFO - [#3] 开始流式生成动作: 'A person is walking forward' (模式: pos)
2025-09-29 18:48:44,563 - WARNING - [#3] WebSocket连接已关闭: received 1011 (internal error) keepalive ping timeout; then sent 1011 (internal error) keepalive ping timeout
2025-09-29 18:52:18,171 - INFO - connection open
2025-09-29 18:52:41,004 - INFO - [#4] 开始流式生成动作: 'A person is walking forward' (模式: pos)
2025-09-29 18:53:26,501 - WARNING - [#4] WebSocket连接已关闭: received 1011 (internal error) keepalive ping timeout; then sent 1011 (internal error) keepalive ping timeout
2025-09-29 18:53:49,200 - INFO - connection open
2025-09-29 18:53:51,016 - INFO - [#5] 开始流式生成动作: 'A person is walking forward' (模式: pos)
2025-09-29 18:55:11,595 - INFO - 提前停止生成，距离阈值: 0.0078 < 0.1
2025-09-29 18:55:14,021 - INFO - [#5] 流式动作生成完成! 耗时: 83.00秒, 数据: 92 frames, 22 joints, 3D positions
2025-09-29 19:02:42,741 - INFO - 正在初始化模型...
2025-09-29 19:02:42,776 - INFO - Use pytorch device_name: cuda
2025-09-29 19:02:42,777 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 19:02:59,708 - INFO - 正在初始化模型...
2025-09-29 19:02:59,715 - INFO - Use pytorch device_name: cuda
2025-09-29 19:02:59,715 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 19:05:06,434 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 19:05:11,002 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-29 19:05:24,721 - INFO - 模型初始化完成! 耗时: 145.01秒
2025-09-29 19:05:24,721 - INFO - 使用设备: cuda
2025-09-29 19:05:24,837 - INFO - server listening on [::1]:8765
2025-09-29 19:05:24,837 - INFO - server listening on 127.0.0.1:8765
2025-09-29 19:05:33,585 - INFO - connection open
2025-09-29 19:05:33,587 - INFO - [#1] 开始流式生成动作: 'a person is walking forward' (模式: pos)
2025-09-29 19:07:07,768 - INFO - 提前停止生成，距离阈值: 0.0125 < 0.1
2025-09-29 19:07:07,780 - INFO - [#1] 流式动作生成完成! 耗时: 94.19秒, 总帧数: 96
2025-09-29 19:07:07,782 - INFO - [#2] 正在生成动作: 'a person is walking forward' (模式: pos)
2025-09-29 19:08:53,920 - INFO - server closing
2025-09-29 19:08:53,921 - INFO - server closed
2025-09-29 19:08:54,306 - ERROR - Task exception was never retrieved
future: <Task finished name='Task-6' coro=<Server.conn_handler() done, defined at C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\server.py:343> exception=KeyboardInterrupt()>
Traceback (most recent call last):
  File "I:\Motion-X\MotionStreamer-main(3)\MotionStreamer-main\websocket_server.py", line 476, in <module>
    asyncio.run(server.start_server())
  File "H:\Python312\Lib\asyncio\runners.py", line 194, in run
    return runner.run(main)
           ^^^^^^^^^^^^^^^^
  File "H:\Python312\Lib\asyncio\runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Python312\Lib\asyncio\base_events.py", line 671, in run_until_complete
    self.run_forever()
  File "H:\Python312\Lib\asyncio\windows_events.py", line 321, in run_forever
    super().run_forever()
  File "H:\Python312\Lib\asyncio\base_events.py", line 638, in run_forever
    self._run_once()
  File "H:\Python312\Lib\asyncio\base_events.py", line 1971, in _run_once
    handle._run()
  File "H:\Python312\Lib\asyncio\events.py", line 84, in _run
    self._context.run(self._callback, *self._args)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python312\site-packages\websockets\asyncio\server.py", line 376, in conn_handler
    await self.handler(connection)
  File "I:\Motion-X\MotionStreamer-main(3)\MotionStreamer-main\websocket_server.py", line 394, in handle_client
    motion_data = self.generate_motion(text, mode)
                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "I:\Motion-X\MotionStreamer-main(3)\MotionStreamer-main\websocket_server.py", line 330, in generate_motion
    motion_latents = self.trans_encoder.sample_for_eval_CFG_inference(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "I:\Motion-X\MotionStreamer-main(3)\MotionStreamer-main\models\llama_model.py", line 188, in sample_for_eval_CFG_inference
    sampled_token_latent = self.diff_loss.sample(mix_conditions, temperature=temperature, cfg=cfg)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "I:\Motion-X\MotionStreamer-main(3)\MotionStreamer-main\models\diffloss.py", line 48, in sample
    sampled_token_latent = self.gen_diffusion.p_sample_loop(
                           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "I:\Motion-X\MotionStreamer-main(3)\MotionStreamer-main\models\diffusion\gaussian_diffusion.py", line 485, in p_sample_loop
    for sample in self.p_sample_loop_progressive(
  File "I:\Motion-X\MotionStreamer-main(3)\MotionStreamer-main\models\diffusion\gaussian_diffusion.py", line 537, in p_sample_loop_progressive
    out = self.p_sample(
          ^^^^^^^^^^^^^^
  File "I:\Motion-X\MotionStreamer-main(3)\MotionStreamer-main\models\diffusion\gaussian_diffusion.py", line 408, in p_sample
    out = self.p_mean_variance(
          ^^^^^^^^^^^^^^^^^^^^^
  File "I:\Motion-X\MotionStreamer-main(3)\MotionStreamer-main\models\diffusion\respace.py", line 92, in p_mean_variance
    return super().p_mean_variance(self._wrap_model(model), *args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "I:\Motion-X\MotionStreamer-main(3)\MotionStreamer-main\models\diffusion\gaussian_diffusion.py", line 309, in p_mean_variance
    model_variance = _extract_into_tensor(model_variance, t, x.shape)
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "I:\Motion-X\MotionStreamer-main(3)\MotionStreamer-main\models\diffusion\gaussian_diffusion.py", line 914, in _extract_into_tensor
    res = th.from_numpy(arr).to(device=timesteps.device)[timesteps].float()
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "H:\Python312\Lib\asyncio\runners.py", line 157, in _on_sigint
    raise KeyboardInterrupt()
KeyboardInterrupt
2025-09-29 19:26:31,653 - INFO - 正在初始化模型...
2025-09-29 19:26:31,718 - INFO - Use pytorch device_name: cuda
2025-09-29 19:26:31,718 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-29 19:27:48,920 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-29 19:27:53,517 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-29 19:28:07,429 - INFO - 模型初始化完成! 耗时: 95.78秒
2025-09-29 19:28:07,429 - INFO - 使用设备: cuda
2025-09-29 19:28:07,514 - INFO - server listening on [::1]:8765
2025-09-29 19:28:07,514 - INFO - server listening on 127.0.0.1:8765
2025-09-29 19:31:36,244 - INFO - connection open
2025-09-29 19:31:38,978 - INFO - [#1] 开始流式生成动作: 'A person is walking forward' (模式: pos)
2025-09-29 19:31:52,651 - INFO - [#1] 首帧生成完成! 耗时: 13.673秒
2025-09-29 19:33:05,219 - INFO - 提前停止生成，距离阈值: 0.0154 < 0.1
2025-09-29 19:33:05,220 - INFO - [#1] 流式动作生成完成! 耗时: 86.24秒, 总帧数: 100
2025-09-29 19:34:08,826 - INFO - [#2] 开始流式生成动作: 'A person is walking forward' (模式: pos)
2025-09-29 19:34:15,599 - INFO - [#2] 首帧生成完成! 耗时: 6.773秒
2025-09-29 19:36:35,546 - INFO - 提前停止生成，距离阈值: 0.0058 < 0.1
2025-09-29 19:36:35,546 - INFO - [#2] 流式动作生成完成! 耗时: 146.72秒, 总帧数: 160
2025-09-30 13:09:33,424 - INFO - 正在初始化模型...
2025-09-30 13:09:33,480 - INFO - Use pytorch device_name: cuda
2025-09-30 13:09:33,480 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-30 13:13:42,282 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-30 13:13:47,233 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-30 13:14:05,565 - INFO - 模型初始化完成! 耗时: 272.12秒
2025-09-30 13:14:05,566 - INFO - 使用设备: cuda
2025-09-30 13:14:05,682 - INFO - server listening on [::1]:8765
2025-09-30 13:14:05,683 - INFO - server listening on 127.0.0.1:8765
2025-09-30 13:19:59,487 - INFO - connection open
2025-09-30 13:20:17,443 - INFO - [#1] 开始流式生成动作: 'A person is walking forward' (模式: pos)
2025-09-30 13:20:29,154 - INFO - [#1] 首帧生成完成! 耗时: 11.711秒
2025-09-30 13:21:52,983 - INFO - 提前停止生成，距离阈值: 0.0154 < 0.1
2025-09-30 13:21:52,984 - INFO - [#1] 流式动作生成完成! 耗时: 95.54秒, 总帧数: 100
2025-09-30 18:50:44,822 - INFO - 正在初始化模型...
2025-09-30 18:50:44,844 - INFO - Use pytorch device_name: cuda
2025-09-30 18:50:44,845 - INFO - Load pretrained SentenceTransformer: sentencet5-xxl/
2025-09-30 18:53:54,573 - INFO - 正在加载检查点: Causal_TAE/net_last.pth
2025-09-30 18:53:58,929 - INFO - 正在加载transformer检查点: Experiments/latest.pth
2025-09-30 18:54:13,030 - INFO - 模型初始化完成! 耗时: 208.21秒
2025-09-30 18:54:13,030 - INFO - 使用设备: cuda
2025-09-30 18:54:13,198 - INFO - server listening on [::1]:8765
2025-09-30 18:54:13,198 - INFO - server listening on 127.0.0.1:8765
2025-09-30 18:56:15,041 - INFO - connection open
2025-09-30 18:56:19,189 - INFO - [#1] 开始流式生成动作: 'A person is walking forward' (模式: pos)
2025-09-30 18:56:42,909 - INFO - [#1] 首帧生成完成! 耗时: 23.720秒
2025-09-30 18:58:20,911 - INFO - 提前停止生成，距离阈值: 0.0154 < 0.1
2025-09-30 18:58:20,911 - INFO - [#1] 流式动作生成完成! 耗时: 121.72秒, 总帧数: 100

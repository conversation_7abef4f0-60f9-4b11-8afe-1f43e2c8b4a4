"""
Blender中直接运行的MMD驱动器调试测试脚本
在Blender的脚本编辑器中运行此脚本来测试MMD驱动功能
"""

import bpy
import json
import os
import sys
import numpy as np

# 添加插件路径到sys.path
addon_path = os.path.join(os.path.dirname(__file__), 'blender_addon')
if addon_path not in sys.path:
    sys.path.append(addon_path)

def create_test_motion_data():
    """创建简单的测试动作数据"""
    print("创建测试动作数据...")
    
    frames = 30  # 30帧测试动画
    motion_data = []
    
    for frame in range(frames):
        frame_data = []
        t = frame / frames * 2 * np.pi  # 一个完整周期
        
        # 创建22个关节的3D位置数据
        for joint in range(22):
            if joint == 0:  # 根关节 - 上下摆动
                x = 0.0
                y = 1.0 + np.sin(t) * 0.1  # 上下摆动
                z = 0.0
            elif joint == 7:  # 头部 - 轻微点头
                x = 0.0
                y = 1.6 + np.sin(t * 2) * 0.02
                z = 0.0
            elif joint == 9:  # 左手臂 - 摆动
                x = np.sin(t) * 0.2
                y = 1.2
                z = 0.0
            elif joint == 13:  # 右手臂 - 反向摆动
                x = np.sin(t + np.pi) * 0.2
                y = 1.2
                z = 0.0
            elif joint == 16:  # 左腿 - 抬腿
                x = 0.0
                y = 0.5 + max(0, np.sin(t)) * 0.1
                z = 0.0
            elif joint == 19:  # 右腿 - 反向抬腿
                x = 0.0
                y = 0.5 + max(0, np.sin(t + np.pi)) * 0.1
                z = 0.0
            else:  # 其他关节 - 基本位置
                x = 0.0
                y = 0.5 + joint * 0.05
                z = 0.0
            
            frame_data.append([x, y, z])
        
        motion_data.append(frame_data)
    
    print(f"测试数据创建完成: {len(motion_data)}帧, 每帧{len(motion_data[0])}个关节")
    return motion_data

def find_armature():
    """查找场景中的骨架对象"""
    print("\n查找骨架对象...")
    
    armatures = [obj for obj in bpy.data.objects if obj.type == 'ARMATURE']
    
    print(f"找到{len(armatures)}个骨架对象:")
    for i, armature in enumerate(armatures):
        bone_count = len(armature.pose.bones) if armature.pose else 0
        print(f"  {i}: {armature.name} ({bone_count}个骨骼)")
    
    if armatures:
        return armatures[0]  # 返回第一个骨架
    else:
        print("❌ 没有找到骨架对象!")
        print("请确保场景中有MMD模型或其他带骨架的对象")
        return None

def test_mmd_driver():
    """测试MMD驱动器"""
    print("="*60)
    print("开始MMD驱动器调试测试")
    print("="*60)
    
    # 查找骨架
    armature = find_armature()
    if not armature:
        return False
    
    # 创建测试数据
    motion_data = create_test_motion_data()
    
    # 尝试导入MMD驱动器
    try:
        import mmd_driver
        print("✅ MMD驱动器模块导入成功")
    except ImportError as e:
        print(f"❌ MMD驱动器模块导入失败: {e}")
        print("请确保blender_addon/mmd_driver.py文件存在")
        return False
    
    # 获取驱动器实例
    driver = mmd_driver.get_mmd_driver()
    print("✅ 获取MMD驱动器实例成功")
    
    # 应用动作数据
    print("\n开始应用测试动作数据...")
    try:
        success = driver.apply_motion_data(armature, motion_data, 'pos')
        
        if success:
            print("✅ 动作数据应用成功!")
            print("🎬 开始播放动画...")
            bpy.ops.screen.animation_play()
        else:
            print("❌ 动作数据应用失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 应用动作数据时出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_scene_info():
    """调试场景信息"""
    print("\n" + "="*40)
    print("场景调试信息")
    print("="*40)
    
    print(f"当前场景: {bpy.context.scene.name}")
    print(f"活动对象: {bpy.context.active_object.name if bpy.context.active_object else 'None'}")
    print(f"当前模式: {bpy.context.mode}")
    
    # 列出所有对象
    print(f"\n场景中的所有对象 ({len(bpy.data.objects)}个):")
    for obj in bpy.data.objects:
        print(f"  - {obj.name} (类型: {obj.type})")
    
    # 列出所有动作
    print(f"\n场景中的所有动作 ({len(bpy.data.actions)}个):")
    for action in bpy.data.actions:
        fcurve_count = len(action.fcurves)
        print(f"  - {action.name} ({fcurve_count}个F-Curve)")

def main():
    """主函数"""
    print("Blender MMD驱动器调试测试开始...")
    
    # 调试场景信息
    debug_scene_info()
    
    # 测试MMD驱动器
    success = test_mmd_driver()
    
    print("\n" + "="*60)
    if success:
        print("🎉 测试完成! 如果模型没有动，请检查:")
        print("1. 骨架是否被正确选择和激活")
        print("2. 骨骼名称是否匹配")
        print("3. 动画时间轴是否设置正确")
        print("4. 查看上面的详细调试信息")
    else:
        print("❌ 测试失败，请查看上面的错误信息")
    print("="*60)

if __name__ == "__main__":
    main()

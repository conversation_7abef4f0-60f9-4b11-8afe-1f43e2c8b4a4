# MMD驱动器调试指南

## 概述

我已经为MotionStreamer添加了详细的调试信息，帮助诊断MMD模型不动的问题。现在系统会输出大量调试信息，帮助我们找到问题所在。

## 调试功能

### 1. 数据接收调试
- 打印接收到的动作数据格式和内容
- 显示数据类型、长度和样本值
- 监控流式数据接收过程

### 2. 骨架分析调试
- 详细分析MMD模型的骨架结构
- 按类别显示所有骨骼名称
- 检查关键骨骼的映射情况

### 3. 动作应用调试
- 逐帧显示动作应用过程
- 显示坐标转换和缩放结果
- 统计成功/失败的骨骼数量
- 检查F-Curve创建情况

## 使用方法

### 方法1: 使用完整插件测试

1. **安装调试版插件**:
   ```
   在Blender中安装 blender_addon/__init__0.py
   ```

2. **启动服务器**:
   ```bash
   python start_server.py
   ```

3. **在Blender中测试**:
   - 加载一个MMD模型
   - 打开Blender控制台 (Window → Toggle System Console)
   - 在MotionStreamer面板中连接服务器
   - 输入动作描述并生成动作
   - 观察控制台输出的详细调试信息

### 方法2: 使用直接测试脚本

1. **在Blender中运行测试脚本**:
   - 打开Blender
   - 加载MMD模型
   - 在脚本编辑器中打开 `blender_debug_test.py`
   - 运行脚本
   - 查看控制台输出

### 方法3: 使用测试插件

1. **安装测试插件**:
   ```
   安装 blender_addon/test_mmd_driver.py
   ```

2. **使用测试功能**:
   - 在"MMD Test"面板中生成测试数据
   - 加载测试数据到模型
   - 观察调试输出

## 调试信息解读

### 数据接收阶段

```
📊 接收到的帧数据样本:
   - 帧数据类型: <class 'list'>
   - 帧数据长度: 22
   - 第一个元素: [0.1, 0.2, 0.3] (类型: <class 'list'>)
```

**正常情况**: 
- 帧数据类型应该是 `list`
- 长度应该是 22 (22个关节)
- 每个元素应该是包含3个数值的列表 `[x, y, z]`

**异常情况**:
- 如果长度不是22，可能是数据格式问题
- 如果元素不是列表，可能是数据结构错误

### 骨架分析阶段

```
🦴 骨架调试信息: Armature
   📊 总骨骼数: 45
   📂 中心/根部 (1个):
      - センター
   📂 身体 (3个):
      - 上半身
      - 上半身2
      - 下半身
```

**检查要点**:
- 确认找到了关键骨骼（センター、上半身等）
- 检查骨骼名称是否符合MMD标准

### 动作应用阶段

```
🔍 分析骨骼映射:
   ✅ 关节0: センター -> センター
   ❌ 关节1: 下半身 -> 未找到
   ✅ 关节7: 頭 -> 頭
```

**成功标志**:
- 大部分关键关节都有 ✅ 标记
- 成功率应该 > 50%
- 创建了F-Curve数量 > 0

## 常见问题诊断

### 问题1: 数据格式错误

**症状**:
```
❌ MMDDriver: 不支持的数据格式
   - 期望格式1: 22个关节，每个关节3个坐标
   - 实际格式: 66个元素，第一个元素类型: <class 'float'>
```

**解决方案**:
- 检查服务器返回的数据格式
- 确认数据是否正确解析

### 问题2: 骨骼映射失败

**症状**:
```
❌ 关节0: センター -> 未找到
❌ 关节7: 頭 -> 未找到
```

**解决方案**:
- 检查MMD模型的骨骼名称
- 可能需要修改 `mmd_driver.py` 中的骨骼映射

### 问题3: 动画未创建

**症状**:
```
📊 应用结果统计:
   ✅ 成功应用的骨骼数: 0
   🎞️  创建的F-Curve数量: 0
```

**解决方案**:
- 检查骨架是否正确激活
- 确认是否在正确的模式下（POSE模式）
- 检查动画数据是否正确创建

### 问题4: 坐标转换问题

**症状**:
- 模型动作异常（过大、过小、方向错误）

**解决方案**:
- 检查坐标转换逻辑
- 调整缩放因子
- 确认坐标系转换是否正确

## 收集调试信息

如果问题仍然存在，请收集以下信息：

1. **Blender控制台的完整输出**
2. **MMD模型的骨骼列表**
3. **接收到的动作数据样本**
4. **骨骼映射结果**
5. **F-Curve创建情况**

## 下一步调试

根据调试输出，我们可以：

1. **修改骨骼映射**: 如果骨骼名称不匹配
2. **调整数据格式处理**: 如果数据格式异常
3. **修改坐标转换**: 如果动作方向或大小异常
4. **优化关键帧创建**: 如果动画数据未正确应用

## 临时解决方案

如果仍然无法解决，可以尝试：

1. **手动测试**: 使用 `blender_debug_test.py` 直接测试
2. **简化数据**: 只测试根骨骼的移动
3. **检查模型**: 确认MMD模型本身没有问题
4. **重新导入**: 重新导入MMD模型并重试

通过这些详细的调试信息，我们应该能够准确定位问题所在并进行针对性修复。

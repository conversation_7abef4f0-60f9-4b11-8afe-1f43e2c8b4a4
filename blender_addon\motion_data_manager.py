"""
MotionStreamer 动作数据管理模块
负责动作数据的保存、加载和管理功能
"""

import os
import json
import time
import numpy as np
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple


class MotionDataManager:
    """动作数据管理器"""
    
    def __init__(self, data_dir: str = None):
        """
        初始化动作数据管理器
        
        Args:
            data_dir: 数据存储目录，默认为用户文档目录下的MotionStreamer文件夹
        """
        if data_dir is None:
            # 默认存储在用户文档目录
            import os
            if os.name == 'nt':  # Windows
                documents_dir = os.path.join(os.path.expanduser('~'), 'Documents')
            else:  # Linux/Mac
                documents_dir = os.path.expanduser('~')
            data_dir = os.path.join(documents_dir, 'MotionStreamer', 'saved_motions')
        
        self.data_dir = data_dir
        self._ensure_data_dir()
        
    def _ensure_data_dir(self):
        """确保数据目录存在"""
        if not os.path.exists(self.data_dir):
            os.makedirs(self.data_dir, exist_ok=True)
            print(f"MotionDataManager: 创建数据目录 {self.data_dir}")
    
    def _generate_filename(self, text: str, mode: str, timestamp: str = None) -> str:
        """
        生成安全的文件名
        
        Args:
            text: 动作描述文本
            mode: 动作模式 ('pos' 或 'rot')
            timestamp: 时间戳，如果为None则自动生成
            
        Returns:
            生成的文件名（不含扩展名）
        """
        if timestamp is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 清理文本，只保留字母数字和基本符号
        safe_text = "".join(c for c in text if c.isalnum() or c in (' ', '-', '_')).strip()
        safe_text = safe_text.replace(' ', '_')[:50]  # 限制长度
        
        if not safe_text:
            safe_text = "motion"
        
        return f"{safe_text}_{mode}_{timestamp}"
    
    def save_motion_data(self, 
                        motion_data: List[List[List[float]]], 
                        text: str, 
                        mode: str = 'pos',
                        metadata: Dict[str, Any] = None) -> str:
        """
        保存动作数据
        
        Args:
            motion_data: 动作数据，格式为 [frame][joint][xyz]
            text: 动作描述文本
            mode: 动作模式 ('pos' 或 'rot')
            metadata: 额外的元数据
            
        Returns:
            保存的文件路径
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            base_filename = self._generate_filename(text, mode, timestamp)
            
            # 准备保存的数据
            save_data = {
                "text": text,
                "mode": mode,
                "timestamp": timestamp,
                "frames": len(motion_data),
                "joints": len(motion_data[0]) if motion_data else 0,
                "motion_data": motion_data,
                "metadata": metadata or {}
            }
            
            # 保存为JSON格式
            json_file = os.path.join(self.data_dir, f"{base_filename}.json")
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, indent=2, ensure_ascii=False)
            
            # 如果数据量大，同时保存为numpy格式以提高加载速度
            if len(motion_data) > 100:  # 超过100帧时保存numpy格式
                try:
                    np_file = os.path.join(self.data_dir, f"{base_filename}.npy")
                    np.save(np_file, np.array(motion_data))
                    save_data["numpy_file"] = f"{base_filename}.npy"
                    
                    # 更新JSON文件以包含numpy文件信息
                    with open(json_file, 'w', encoding='utf-8') as f:
                        json.dump(save_data, f, indent=2, ensure_ascii=False)
                        
                    print(f"MotionDataManager: 同时保存numpy格式 {np_file}")
                except Exception as e:
                    print(f"MotionDataManager: 保存numpy格式失败 {e}")
            
            print(f"MotionDataManager: 动作数据已保存")
            print(f"  文件: {json_file}")
            print(f"  描述: {text}")
            print(f"  模式: {mode}")
            print(f"  帧数: {len(motion_data)}")
            print(f"  关节数: {len(motion_data[0]) if motion_data else 0}")
            
            return json_file
            
        except Exception as e:
            print(f"MotionDataManager: 保存动作数据失败 {e}")
            raise
    
    def load_motion_data(self, filepath: str) -> Optional[Dict[str, Any]]:
        """
        加载动作数据
        
        Args:
            filepath: 文件路径
            
        Returns:
            包含动作数据的字典，如果加载失败返回None
        """
        try:
            if not os.path.exists(filepath):
                print(f"MotionDataManager: 文件不存在 {filepath}")
                return None
            
            # 加载JSON文件
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 如果有numpy文件，优先加载numpy数据
            if "numpy_file" in data:
                np_file = os.path.join(os.path.dirname(filepath), data["numpy_file"])
                if os.path.exists(np_file):
                    try:
                        motion_data = np.load(np_file).tolist()
                        data["motion_data"] = motion_data
                        print(f"MotionDataManager: 从numpy文件加载动作数据 {np_file}")
                    except Exception as e:
                        print(f"MotionDataManager: 加载numpy文件失败，使用JSON数据 {e}")
            
            print(f"MotionDataManager: 动作数据加载成功")
            print(f"  文件: {filepath}")
            print(f"  描述: {data.get('text', 'N/A')}")
            print(f"  模式: {data.get('mode', 'N/A')}")
            print(f"  帧数: {data.get('frames', 0)}")
            print(f"  关节数: {data.get('joints', 0)}")
            
            return data
            
        except Exception as e:
            print(f"MotionDataManager: 加载动作数据失败 {e}")
            return None
    
    def list_saved_motions(self) -> List[Dict[str, Any]]:
        """
        列出所有保存的动作数据
        
        Returns:
            动作数据信息列表
        """
        motions = []
        
        try:
            if not os.path.exists(self.data_dir):
                return motions
            
            for filename in os.listdir(self.data_dir):
                if filename.endswith('.json'):
                    filepath = os.path.join(self.data_dir, filename)
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        motion_info = {
                            "filename": filename,
                            "filepath": filepath,
                            "text": data.get('text', 'N/A'),
                            "mode": data.get('mode', 'N/A'),
                            "timestamp": data.get('timestamp', 'N/A'),
                            "frames": data.get('frames', 0),
                            "joints": data.get('joints', 0),
                            "file_size": os.path.getsize(filepath)
                        }
                        motions.append(motion_info)
                        
                    except Exception as e:
                        print(f"MotionDataManager: 读取文件信息失败 {filename}: {e}")
            
            # 按时间戳排序，最新的在前
            motions.sort(key=lambda x: x['timestamp'], reverse=True)
            
        except Exception as e:
            print(f"MotionDataManager: 列出动作数据失败 {e}")
        
        return motions
    
    def delete_motion_data(self, filepath: str) -> bool:
        """
        删除动作数据文件
        
        Args:
            filepath: 要删除的文件路径
            
        Returns:
            删除是否成功
        """
        try:
            if not os.path.exists(filepath):
                print(f"MotionDataManager: 文件不存在 {filepath}")
                return False
            
            # 如果有对应的numpy文件，也一起删除
            if filepath.endswith('.json'):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    if "numpy_file" in data:
                        np_file = os.path.join(os.path.dirname(filepath), data["numpy_file"])
                        if os.path.exists(np_file):
                            os.remove(np_file)
                            print(f"MotionDataManager: 删除numpy文件 {np_file}")
                except Exception as e:
                    print(f"MotionDataManager: 删除numpy文件时出错 {e}")
            
            # 删除主文件
            os.remove(filepath)
            print(f"MotionDataManager: 删除文件成功 {filepath}")
            return True
            
        except Exception as e:
            print(f"MotionDataManager: 删除文件失败 {e}")
            return False
    
    def get_data_dir(self) -> str:
        """获取数据存储目录"""
        return self.data_dir
    
    def get_storage_info(self) -> Dict[str, Any]:
        """
        获取存储信息
        
        Returns:
            存储信息字典
        """
        info = {
            "data_dir": self.data_dir,
            "total_files": 0,
            "total_size": 0,
            "json_files": 0,
            "numpy_files": 0
        }
        
        try:
            if os.path.exists(self.data_dir):
                for filename in os.listdir(self.data_dir):
                    filepath = os.path.join(self.data_dir, filename)
                    if os.path.isfile(filepath):
                        info["total_files"] += 1
                        info["total_size"] += os.path.getsize(filepath)
                        
                        if filename.endswith('.json'):
                            info["json_files"] += 1
                        elif filename.endswith('.npy'):
                            info["numpy_files"] += 1
        
        except Exception as e:
            print(f"MotionDataManager: 获取存储信息失败 {e}")
        
        return info


# 全局实例
_motion_data_manager = None

def get_motion_data_manager() -> MotionDataManager:
    """获取全局动作数据管理器实例"""
    global _motion_data_manager
    if _motion_data_manager is None:
        _motion_data_manager = MotionDataManager()
    return _motion_data_manager

"""
演示离线使用保存的动作数据
这个脚本展示了如何在不启动服务器的情况下使用之前保存的动作数据
"""

import sys
import os
import json
import numpy as np

# 添加blender_addon目录到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'blender_addon'))

from motion_data_manager import get_motion_data_manager

def create_sample_motions():
    """创建一些示例动作数据"""
    manager = get_motion_data_manager()
    
    print("🎭 创建示例动作数据...")
    
    # 创建行走动作
    walking_motion = create_walking_motion()
    manager.save_motion_data(
        motion_data=walking_motion,
        text="人物向前行走",
        mode="pos",
        metadata={"type": "locomotion", "speed": "normal"}
    )
    
    # 创建挥手动作
    waving_motion = create_waving_motion()
    manager.save_motion_data(
        motion_data=waving_motion,
        text="友好挥手打招呼",
        mode="pos",
        metadata={"type": "gesture", "emotion": "friendly"}
    )
    
    # 创建跳跃动作
    jumping_motion = create_jumping_motion()
    manager.save_motion_data(
        motion_data=jumping_motion,
        text="原地跳跃动作",
        mode="pos",
        metadata={"type": "action", "intensity": "high"}
    )
    
    print("✅ 示例动作数据创建完成")

def create_walking_motion():
    """创建行走动作数据"""
    frames = 80
    joints = 22
    motion_data = []
    
    for frame in range(frames):
        frame_data = []
        t = frame / frames * 4 * np.pi  # 两个步态周期
        
        for joint in range(joints):
            if joint == 0:  # 根关节 - 向前移动
                x = frame * 0.03
                y = 0.0
                z = np.sin(t * 2) * 0.02
            elif joint in [16, 19]:  # 腿部 - 交替抬腿
                x = 0.0
                y = np.sin(t + (np.pi if joint == 19 else 0)) * 0.15
                z = 0.0
            elif joint in [9, 13]:  # 手臂 - 摆臂
                x = np.sin(t + (np.pi if joint == 13 else 0)) * 0.1
                y = 0.0
                z = 0.0
            elif joint == 4:  # 颈部 - 轻微点头
                x = 0.0
                y = 1.5 + np.sin(t * 4) * 0.01
                z = 0.0
            else:
                x = np.sin(t) * 0.005
                y = 0.5 + joint * 0.05
                z = 0.0
            
            frame_data.append([x, y, z])
        motion_data.append(frame_data)
    
    return motion_data

def create_waving_motion():
    """创建挥手动作数据"""
    frames = 60
    joints = 22
    motion_data = []
    
    for frame in range(frames):
        frame_data = []
        t = frame / frames * 2 * np.pi
        
        for joint in range(joints):
            if joint == 0:  # 根关节 - 保持稳定
                x = 0.0
                y = 0.0
                z = 0.0
            elif joint == 9:  # 左手臂 - 大幅挥动
                x = np.sin(t * 3) * 0.3
                y = 1.2 + np.sin(t * 2) * 0.2
                z = 0.0
            elif joint == 10:  # 左前臂
                x = np.sin(t * 4) * 0.2
                y = 1.4 + np.sin(t * 3) * 0.15
                z = 0.0
            elif joint == 11:  # 左手
                x = np.sin(t * 5) * 0.1
                y = 1.6 + np.sin(t * 4) * 0.1
                z = 0.0
            elif joint == 4:  # 颈部 - 轻微转动
                x = np.sin(t) * 0.02
                y = 1.5
                z = 0.0
            else:
                x = np.sin(t) * 0.002
                y = 0.5 + joint * 0.05
                z = 0.0
            
            frame_data.append([x, y, z])
        motion_data.append(frame_data)
    
    return motion_data

def create_jumping_motion():
    """创建跳跃动作数据"""
    frames = 40
    joints = 22
    motion_data = []
    
    for frame in range(frames):
        frame_data = []
        t = frame / frames
        
        # 跳跃轨迹：蹲下 -> 起跳 -> 空中 -> 落地
        if t < 0.2:  # 蹲下准备
            jump_height = -0.1 * (1 - t/0.2)
        elif t < 0.4:  # 起跳
            jump_height = 0.3 * ((t-0.2)/0.2)
        elif t < 0.7:  # 空中
            jump_height = 0.3
        else:  # 落地
            jump_height = 0.3 * (1 - (t-0.7)/0.3)
        
        for joint in range(joints):
            if joint == 0:  # 根关节 - 垂直跳跃
                x = 0.0
                y = jump_height
                z = 0.0
            elif joint in [16, 17, 18, 19, 20, 21]:  # 腿部 - 弯曲伸展
                bend_factor = 1.0 - abs(jump_height) * 2
                x = 0.0
                y = 0.3 + joint * 0.05 + bend_factor * 0.1
                z = 0.0
            elif joint in [9, 13]:  # 手臂 - 向上摆动
                x = 0.0
                y = 1.0 + jump_height * 0.5
                z = 0.0
            else:
                x = 0.0
                y = 0.5 + joint * 0.05
                z = 0.0
            
            frame_data.append([x, y, z])
        motion_data.append(frame_data)
    
    return motion_data

def demonstrate_offline_usage():
    """演示离线使用功能"""
    print("=" * 60)
    print("MotionStreamer 离线使用演示")
    print("=" * 60)
    
    manager = get_motion_data_manager()
    
    # 检查是否有保存的动作数据
    motions = manager.list_saved_motions()
    
    if not motions:
        print("📝 没有找到保存的动作数据，创建一些示例...")
        create_sample_motions()
        motions = manager.list_saved_motions()
    
    print(f"\n📋 找到 {len(motions)} 个保存的动作:")
    for i, motion in enumerate(motions):
        print(f"   {i+1}. {motion['text']}")
        print(f"      模式: {motion['mode']}, 帧数: {motion['frames']}")
        print(f"      时间: {motion['timestamp']}")
        print(f"      大小: {motion['file_size'] / 1024:.1f} KB")
        print()
    
    # 演示加载和使用动作数据
    print("🎬 演示加载动作数据...")
    for i, motion in enumerate(motions[:3]):  # 只演示前3个
        print(f"\n--- 加载动作 {i+1}: {motion['text']} ---")
        
        try:
            data = manager.load_motion_data(motion['filepath'])
            if data:
                print(f"✅ 加载成功:")
                print(f"   描述: {data['text']}")
                print(f"   帧数: {data['frames']}")
                print(f"   关节数: {data['joints']}")
                print(f"   元数据: {data.get('metadata', {})}")
                
                # 分析动作数据
                motion_data = data['motion_data']
                if motion_data:
                    # 计算动作范围
                    all_positions = []
                    for frame in motion_data:
                        for joint in frame:
                            all_positions.extend(joint)
                    
                    min_pos = min(all_positions)
                    max_pos = max(all_positions)
                    
                    print(f"   动作范围: {min_pos:.3f} ~ {max_pos:.3f}")
                    print(f"   第一帧第一个关节位置: {motion_data[0][0]}")
                
                print("   💡 这个动作数据可以直接在Blender中使用，无需服务器连接")
            else:
                print("❌ 加载失败")
        except Exception as e:
            print(f"❌ 加载出错: {e}")
    
    # 显示存储统计
    print(f"\n📊 存储统计:")
    storage_info = manager.get_storage_info()
    print(f"   数据目录: {storage_info['data_dir']}")
    print(f"   总文件数: {storage_info['total_files']}")
    print(f"   JSON文件: {storage_info['json_files']}")
    print(f"   Numpy文件: {storage_info['numpy_files']}")
    print(f"   总大小: {storage_info['total_size'] / 1024:.1f} KB")
    
    print(f"\n💡 使用说明:")
    print(f"   1. 这些动作数据已保存在本地，可以离线使用")
    print(f"   2. 在Blender中使用插件的'加载动作数据'功能")
    print(f"   3. 选择任意JSON文件即可加载动作")
    print(f"   4. 无需启动MotionStreamer服务器")
    print(f"   5. 适合调试和重复使用动作数据")
    
    print("\n" + "=" * 60)
    print("演示完成")
    print("=" * 60)

if __name__ == "__main__":
    demonstrate_offline_usage()
